package com.project.management.beneficiaries

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class BeneficiaryService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<Beneficiary> {
        val user = currentUser.getCurrentUser()
        val beneficiaries = BeneficiaryRepository.Query.getAll(user)
        return beneficiaries
    }

    fun getById(beneficiaryId: Long): Beneficiary {
        val user = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository.Query.getById(user, beneficiaryId).validate()
        return beneficiary
    }

    @Transactional
    fun create(request: BeneficiaryPostRequest): Beneficiary {
        val user = currentUser.getCurrentUser()
        val beneficiary = request.toModel(user)

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }

    @Transactional
    fun edit(
        request: BeneficiaryPatchPatch,
        beneficiaryId: Long
    ): Beneficiary {
        val loggedInUser = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository.Query
            .getById(loggedInUser, beneficiaryId).validate()

        if (beneficiary.version != request.version) throw BusinessException.ConflictException()

        if (request.name != null) beneficiary.name = request.name
        if (request.phoneNumber != null) {
            beneficiary.phoneNumber = request.phoneNumber
            beneficiary.secondaryPhoneNumber = request.phoneNumber
        }
        beneficiary.updatedBy = loggedInUser.id

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }
}

@Service
class BeneficiaryTransactionQueryService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiaryTransactions = BeneficiaryTransactionRepository.Query.getAll(OrganizationId(user.organizationId))
        return beneficiaryTransactions
    }

    fun getAllByBeneficiaryId(beneficiaryId: Long): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository
            .Query.getById(OrganizationId(user.organizationId), beneficiaryId).validate()

        val beneficiaryTransactions = BeneficiaryTransactionRepository.Query.getAllByBeneficiaryId(
            organizationId = OrganizationId(user.organizationId),
            beneficiaryId = beneficiary.id!!
        )
        return beneficiaryTransactions
    }
}

@Service
class BeneficiaryTransactionMutateService(
    private val currentUser: CurrentUserConfig,
) {
    @Transactional
    fun updateTransaction(
        request: BeneficiaryTransactionPatchRequest,
        beneficiaryTransactionId: Long
    ): BeneficiaryTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = request.validate(beneficiaryTransactionId, loggedInUser)

        // Update properties manually since we're no longer using data classes with .copy()
        if (request.description != null) {
            transaction.description = request.description
        }
        if (request.transactionDate != null) {
            transaction.transactionDate = ZonedDateTime.parse(request.transactionDate)
        }
        transaction.updatedBy = loggedInUser.id!!

        return BeneficiaryTransactionRepository.Mutate.save(transaction)
    }
}