package com.project.management.beneficiaries

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/beneficiaries")
class BeneficiaryController(
    private val beneficiaryService: BeneficiaryService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Beneficiary>> {
        return ResponseEntity.ok(beneficiaryService.getAll())
    }

    @GetMapping("/{beneficiaryId}")
    fun getById(@PathVariable beneficiaryId: Long): ResponseEntity<Beneficiary> {
        return ResponseEntity.ok(beneficiaryService.getById(beneficiaryId))
    }

    @PostMapping
    fun create(@RequestBody beneficiaryPostRequest: BeneficiaryPostRequest): ResponseEntity<Beneficiary> {
        return ResponseEntity.ok(beneficiaryService.create(beneficiaryPostRequest))
    }

    @PatchMapping("/{beneficiaryId}")
    fun edit(
        @PathVariable beneficiaryId: Long,
        @RequestBody request: BeneficiaryPatchPatch,
    ): ResponseEntity<Beneficiary> {
        return ResponseEntity.ok(beneficiaryService.edit(request, beneficiaryId))
    }
}

@RestController
@RequestMapping("/api/v1/beneficiaries")
class BeneficiaryTransactionController(
    private val query: BeneficiaryTransactionQueryService,
    private val mutate: BeneficiaryTransactionMutateService,
) {
    @GetMapping("/{beneficiaryId}/transactions")
    fun getAll(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<BeneficiaryTransaction>> {
        return ResponseEntity.ok(query.getAllByBeneficiaryId(beneficiaryId))
    }

    @PatchMapping("/{beneficiaryId}/transactions/{beneficiaryTransactionId}")
    fun modifyTransactionOld(
        @PathVariable beneficiaryTransactionId: Long,
        @RequestBody request: BeneficiaryTransactionPatchRequest
    ): ResponseEntity<BeneficiaryTransaction> {
        return ResponseEntity.ok(mutate.updateTransaction(request, beneficiaryTransactionId))
    }

    @PatchMapping("/transactions/{beneficiaryTransactionId}")
    fun modifyTransaction(
        @PathVariable beneficiaryTransactionId: Long,
        @RequestBody request: BeneficiaryTransactionPatchRequest
    ): ResponseEntity<BeneficiaryTransaction> {
        return ResponseEntity.ok(mutate.updateTransaction(request, beneficiaryTransactionId))
    }
}