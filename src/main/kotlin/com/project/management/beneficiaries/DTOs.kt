package com.project.management.beneficiaries

import com.project.management.common.entity.OrganizationId
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.Injection
import com.project.management.users.User

data class BeneficiaryPostRequest(
    val name: String,
    val phoneNumber: String?,
    val secondaryPhoneNumber: String?,
    val phone_number: String?, // TODO Remove
    val secondary_phone_number: String?, // TODO Remove
)

data class BeneficiaryPatchPatch(
    val name: String?,
    val phoneNumber: String?,
    val version: Long
)

data class BeneficiaryTransactionPostRequest(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val projectId: Long,
    val transactionDate: String
) {
    fun validate(beneficiaryId: Long?, user: User) {
        // Validate project (required)
        Injection.projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        // Validate amount constraints
        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        // Early return if no beneficiary provided
        if (beneficiaryId == null) return

        // Validate beneficiary if provided
        BeneficiaryRepository.Query.getById(
            id = beneficiaryId,
            organizationId = OrganizationId(user.organizationId)
        )
    }
}

data class BeneficiaryTransactionPatchRequest(
    val description: String? = null,
    val transactionDate: String? = null,
    val version: Long
) {
    fun validate(beneficiaryTransactionId: Long, user: User): BeneficiaryTransaction {
        val transaction = Injection.beneficiaryTransactionsValidator.validateExistsByIdAndOrganization(
            beneficiaryTransactionId = beneficiaryTransactionId,
            organizationId = user.organizationId
        )
        if (transaction.version != version) throw BusinessException.ConflictException()

        return transaction
    }
}