package com.project.management.beneficiaries

import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.users.User
import java.time.ZonedDateTime

fun BeneficiaryPostRequest.toModel(performer: User): Beneficiary {
    return Beneficiary(
        name = name,
        phoneNumber = phoneNumber ?: phone_number!!,
        secondaryPhoneNumber = secondaryPhoneNumber ?: secondary_phone_number!!,
        balanceAccumulator = 0.0.toBigDecimal(),
        paidAccumulator = 0.0.toBigDecimal(),
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
    )
}

fun BeneficiaryTransactionPostRequest.toEntity(
    beneficiaryId: Long?,
    user: User,
    version: Long
): BeneficiaryTransaction {
    // Validate the request with the beneficiaryId
    this.validate(beneficiaryId, user)

    return BeneficiaryTransaction(
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        beneficiaryId = beneficiaryId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdByDetails = user,
        organizationId = user.organizationId,
        createdBy = user.id!!,
        updatedBy = user.id!!,
        version = version
    )
}

fun PostRequestProjectExpense.toBeneficiaryTransaction(projectId: Long): BeneficiaryTransactionPostRequest {
    return BeneficiaryTransactionPostRequest(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

internal fun PostRequestProjectExpense.toEntity(
    projectId: Long,
    beneficiaryTransaction: BeneficiaryTransaction,
    user: User,
    version: Long
): ProjectExpenseEntity {
    return ProjectExpenseEntity(
        organizationId = user.organizationId,
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransaction.id!!,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        createdBy = user.id,
        updatedBy = user.id,
        version = version
    )
}