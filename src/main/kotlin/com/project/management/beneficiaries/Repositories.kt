package com.project.management.beneficiaries

import com.project.management.common.entity.OrganizationId
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object BeneficiaryRepository {
    private val repo: SpringBeneficiaryRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Beneficiary> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Beneficiary? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumAllBalance(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumBalanceAccumulatorByOrganizationId(organizationId.value)
        }

        fun sumAllPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumPaidAccumulatorByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(beneficiary: Beneficiary): Beneficiary {
            return repo.save(beneficiary)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(BENEFICIARY_DELETED_FILTER, block)
}

object BeneficiaryTransactionRepository {
    private val queryRepo: SpringBeneficiaryTransactionQueryRepository by autowired()
    private val mutateRepo: SpringBeneficiaryTransactionMutateRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<BeneficiaryTransaction> = filterDeleted {
            queryRepo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByBeneficiaryId(
            organizationId: OrganizationId,
            beneficiaryId: Long
        ): List<BeneficiaryTransaction> = filterDeleted {
            queryRepo.findAllByOrganizationIdAndBeneficiaryId(organizationId.value, beneficiaryId)
        }

        fun getById(organizationId: OrganizationId, id: Long): BeneficiaryTransaction? = filterDeleted {
            queryRepo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getByIdValidated(organizationId: OrganizationId, id: Long): BeneficiaryTransaction = filterDeleted {
            queryRepo.findByIdAndOrganizationId(id, organizationId.value)
                ?: throw BusinessException.NotFoundException("Beneficiary transaction with id $id not found")
        }

        fun sumAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumAmountPaidByOrganizationId(organizationId.value)
        }

        fun sumAmountByBeneficiary(organizationId: OrganizationId, beneficiaryId: Long): BigDecimal = filterDeleted {
            queryRepo.sumAmountByOrganizationIdAndBeneficiaryId(organizationId.value, beneficiaryId)
        }

        fun sumAmountPaidByBeneficiary(organizationId: OrganizationId, beneficiaryId: Long): BigDecimal = filterDeleted {
            queryRepo.sumAmountPaidByOrganizationIdAndBeneficiaryId(organizationId.value, beneficiaryId)
        }

        fun sumGeneralAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumGeneralAmountByOrganizationId(organizationId.value)
        }

        fun sumGeneralAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            queryRepo.sumGeneralAmountPaidByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(transaction: BeneficiaryTransaction): BeneficiaryTransaction {
            return mutateRepo.save(transaction)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            mutateRepo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(BENEFICIARY_TRANSACTION_DELETED_FILTER, block)
}

@Repository
private interface SpringBeneficiaryRepository : JpaRepository<Beneficiary, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Beneficiary>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Beneficiary?

    // SUM queries for beneficiary accumulators
    @Query(
        value = "SELECT COALESCE(SUM(balance_accumulator), 0) FROM beneficiaries WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceAccumulatorByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(paid_accumulator), 0) FROM beneficiaries WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumPaidAccumulatorByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringBeneficiaryTransactionQueryRepository : JpaRepository<BeneficiaryTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<BeneficiaryTransaction>

    fun findAllByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): List<BeneficiaryTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): BeneficiaryTransaction?

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id = :beneficiaryId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id = :beneficiaryId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id IS NULL AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumGeneralAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM beneficiary_transactions WHERE organization_id = :organizationId AND beneficiary_id IS NULL AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumGeneralAmountPaidByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringBeneficiaryTransactionMutateRepository : JpaRepository<BeneficiaryTransaction, Long> {
    @Modifying
    @Query(
        value = "UPDATE beneficiary_transactions SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}